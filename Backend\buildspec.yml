version: 0.2

phases:
  install:
    commands:
      - echo "Installing npm dependencies in CodeBuild..."
      - npm install # Installs all your node_modules, including 'prisma' and '@prisma/client'

  pre_build: # This phase runs before the 'build' phase
    commands:
      - echo "Generating Prisma Client in CodeBuild..."
      - npx prisma generate # Generate the client code BEFORE TypeScript compilation

  build: # This phase is for compiling your  application
    commands:
      - echo "Compiling TypeScript to JavaScript (npm run build)..."
      - npm run build # Now 'tsc' can find the generated Prisma Client

  post_build:
    commands:
      - echo "Build complete."

artifacts:
  files:
    - '**/*'
  exclude:
    - node_modules/**/*
    - node_modules
  base-directory: 
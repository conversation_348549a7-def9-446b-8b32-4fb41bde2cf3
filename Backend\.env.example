# Database
DATABASE_URL="your_mongodb_connection_string"

# JWT Configuration
JWT_SECRET="your_jwt_secret_key"
JWT_REFRESH_SECRET="your_jwt_refresh_secret_key"
JWT_EXPIRES_IN="7D"
JWT_REFRESH_EXPIRES_IN="7D"

# Email Configuration
EMAIL_USER="<EMAIL>"
EMAIL_APP_PASSWORD="your_app_password"

# Google OAuth
GOOGLE_CLIENT_ID="your_google_client_id"
GOOGLE_CLIENT_SECRET="your_google_client_secret"

# AWS S3 Configuration (for image uploads)
AWS_ACCESS_KEY_ID="your_aws_access_key_id"
AWS_SECRET_ACCESS_KEY="your_aws_secret_access_key"
AWS_REGION="ap-southeast-2"
AWS_S3_BUCKET_NAME="ptebydee-uploads"

# Application Configuration
NODE_ENV="development"
PORT=5000
FRONTEND_URL="http://localhost:3000"
